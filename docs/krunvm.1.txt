krunvm(1)
=========

NAME
----
krunvm - Create microVMs from OCI images


SYNOPSIS
--------
*krunvm* [_GLOBAL_OPTIONS_] *command*


DESCRIPTION
-----------
krunvm is a CLI utility to create, manage and start microVMs which are
generated from OCI images, providing an interface that resembles
operating on conventional containers.

krunvm uses buildah(1) to download the OCI image and mount it into a
local directory, and libkrun to launch the microVM.

The local directory where the OCI image has been mounted is used as
the root filesystem for the microVM, serviced by a virtio-fs
device/server bundled into libkrun.

krunvm supports mounting additional local directories into the
microVM and exposing ports from the guest to the host (and the
networks connected to it).

Networking to the guest running in the microVM is provided by
libkrun's TSI (Transparent Socket Impersonation), enabling a seamless
experience that doesn't require network bridges nor other explicit
network configuration.


GLOBAL OPTIONS
--------------
*-v* _NUM_::
  Sets the verbosity level, from the lowest (0) to the highest (5).


COMMANDS
--------
|===
|Command | Description

|krunvm-changevm(1) | Change the configuration of a microVM
|krunvm-config(1) | Configure global values
|krunvm-create(1) | Create a new microVM
|krunvm-delete(1) | Delete an existing microVM
|krunvm-list(1) | List the existing microVMs
|krunvm-start(1) | Start an existing microVM
|===


SEE ALSO
--------
*buildah(1)*
