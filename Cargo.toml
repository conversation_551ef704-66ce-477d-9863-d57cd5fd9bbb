[package]
name = "krunvm"
version = "0.2.3"
authors = ["<PERSON> <<EMAIL>>"]
description = "Create microVMs from OCI images"
repository = "https://github.com/containers/krunvm"
license = "Apache-2.0"
edition = "2018"
build = "build.rs"

[dependencies]
clap = {version = "4.4.6", features = ["derive"]}
confy = "0.4.0"
libc = "0.2.82"
serde = "1.0.120"
serde_derive = "1.0.120"
text_io = "0.1.8"
nix = {version = "0.27.1", features = ["socket", "fs"]}
oci-distribution = "0.11.0"
