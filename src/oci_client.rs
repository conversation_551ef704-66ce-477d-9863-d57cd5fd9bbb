// Copyright 2021 Red Hat, Inc.
// SPDX-License-Identifier: Apache-2.0

use std::fs::{self, File};
use std::io::Write;
use std::path::{Path, PathBuf};

use flate2::read::GzDecoder;
use oci_distribution::{Client, Reference};
use serde_json::Value;
use sha2::{Digest, Sha256};
use tar::Archive;
use tokio::runtime::Runtime;

use crate::{KrunvmConfig, VmConfig};

pub struct OciClient {
    client: Client,
    runtime: Runtime,
    storage_root: PathBuf,
}

#[derive(Debug, Clone)]
pub struct ImageInfo {
    pub config: Value,
    pub manifest: Value,
    pub rootfs_path: PathBuf,
    pub container_id: String,
}

impl OciClient {
    pub fn new(cfg: &KrunvmConfig) -> Result<Self, Box<dyn std::error::Error>> {
        let client = Client::default();
        let runtime = Runtime::new()?;
        let storage_root = PathBuf::from(&cfg.storage_volume).join("oci-storage");
        
        // Create storage directories
        fs::create_dir_all(&storage_root)?;
        fs::create_dir_all(storage_root.join("images"))?;
        fs::create_dir_all(storage_root.join("containers"))?;
        fs::create_dir_all(storage_root.join("rootfs"))?;
        
        Ok(OciClient {
            client,
            runtime,
            storage_root,
        })
    }

    pub fn pull_image(&self, image_ref: &str) -> Result<ImageInfo, Box<dyn std::error::Error>> {
        let reference: Reference = image_ref.parse()?;

        self.runtime.block_on(async {
            // Pull the image
            let image_data = self.client.pull(
                &reference,
                &oci_distribution::secrets::RegistryAuth::Anonymous,
                vec![]
            ).await?;

            // Generate container ID
            let container_id = self.generate_container_id(&reference);

            // Create container directory
            let container_dir = self.storage_root.join("containers").join(&container_id);
            fs::create_dir_all(&container_dir)?;

            // Create rootfs directory
            let rootfs_path = self.storage_root.join("rootfs").join(&container_id);
            fs::create_dir_all(&rootfs_path)?;

            // Extract layers to rootfs
            for layer in &image_data.layers {
                self.extract_layer(layer, &rootfs_path)?;
            }

            // Save image config
            let config_path = container_dir.join("config.json");
            let mut config_file = File::create(config_path)?;
            config_file.write_all(&serde_json::to_vec_pretty(&image_data.config)?)?;

            // Save manifest
            let manifest_path = container_dir.join("manifest.json");
            let mut manifest_file = File::create(manifest_path)?;
            manifest_file.write_all(&serde_json::to_vec_pretty(&image_data.manifest)?)?;

            Ok(ImageInfo {
                config: image_data.config,
                manifest: image_data.manifest,
                rootfs_path,
                container_id,
            })
        })
    }

    pub fn inspect_container(&self, container_id: &str) -> Result<Value, Box<dyn std::error::Error>> {
        let config_path = self.storage_root
            .join("containers")
            .join(container_id)
            .join("config.json");
            
        if !config_path.exists() {
            return Err(format!("Container {} not found", container_id).into());
        }
        
        let config_data = fs::read_to_string(config_path)?;
        let config: Value = serde_json::from_str(&config_data)?;
        Ok(config)
    }

    pub fn mount_container(&self, container_id: &str) -> Result<String, Box<dyn std::error::Error>> {
        let rootfs_path = self.storage_root.join("rootfs").join(container_id);
        
        if !rootfs_path.exists() {
            return Err(format!("Container {} rootfs not found", container_id).into());
        }
        
        Ok(rootfs_path.to_string_lossy().to_string())
    }

    pub fn umount_container(&self, _container_id: &str) -> Result<(), Box<dyn std::error::Error>> {
        // In our implementation, we don't need to explicitly unmount
        // since we're using direct filesystem access
        Ok(())
    }

    pub fn remove_container(&self, container_id: &str) -> Result<(), Box<dyn std::error::Error>> {
        let container_dir = self.storage_root.join("containers").join(container_id);
        let rootfs_dir = self.storage_root.join("rootfs").join(container_id);
        
        if container_dir.exists() {
            fs::remove_dir_all(container_dir)?;
        }
        
        if rootfs_dir.exists() {
            fs::remove_dir_all(rootfs_dir)?;
        }
        
        Ok(())
    }

    fn generate_container_id(&self, reference: &Reference) -> String {
        let mut hasher = Sha256::new();
        hasher.update(reference.whole().as_bytes());
        hasher.update(chrono::Utc::now().timestamp().to_string().as_bytes());
        format!("{:x}", hasher.finalize())[..16].to_string()
    }

    fn extract_layer(&self, layer_data: &[u8], rootfs_path: &Path) -> Result<(), Box<dyn std::error::Error>> {
        let decoder = GzDecoder::new(layer_data);
        let mut archive = Archive::new(decoder);
        
        for entry in archive.entries()? {
            let mut entry = entry?;
            let path = entry.path()?;
            let target_path = rootfs_path.join(&path);
            
            // Create parent directories
            if let Some(parent) = target_path.parent() {
                fs::create_dir_all(parent)?;
            }
            
            // Extract the entry
            entry.unpack(&target_path)?;
        }
        
        Ok(())
    }
}

// Helper function to create OCI client
pub fn create_oci_client(cfg: &KrunvmConfig) -> Result<OciClient, Box<dyn std::error::Error>> {
    OciClient::new(cfg)
}

// Helper function to pull and create container (replaces buildah from)
pub fn create_container_from_image(
    cfg: &KrunvmConfig,
    image_ref: &str,
) -> Result<String, Box<dyn std::error::Error>> {
    let client = create_oci_client(cfg)?;
    let image_info = client.pull_image(image_ref)?;
    Ok(image_info.container_id)
}

// Helper function to inspect container (replaces buildah inspect)
pub fn inspect_container(
    cfg: &KrunvmConfig,
    container_id: &str,
) -> Result<String, Box<dyn std::error::Error>> {
    let client = create_oci_client(cfg)?;
    let config = client.inspect_container(container_id)?;
    Ok(serde_json::to_string_pretty(&config)?)
}

// Helper function to mount container (replaces buildah mount)
pub fn mount_container_oci(
    cfg: &KrunvmConfig,
    vmcfg: &VmConfig,
) -> Result<String, Box<dyn std::error::Error>> {
    let client = create_oci_client(cfg)?;
    client.mount_container(&vmcfg.container)
}

// Helper function to unmount container (replaces buildah umount)
pub fn umount_container_oci(
    cfg: &KrunvmConfig,
    vmcfg: &VmConfig,
) -> Result<(), Box<dyn std::error::Error>> {
    let client = create_oci_client(cfg)?;
    client.umount_container(&vmcfg.container)
}

// Helper function to remove container (replaces buildah rm)
pub fn remove_container_oci(
    cfg: &KrunvmConfig,
    vmcfg: &VmConfig,
) -> Result<(), Box<dyn std::error::Error>> {
    let client = create_oci_client(cfg)?;
    client.remove_container(&vmcfg.container)
}
